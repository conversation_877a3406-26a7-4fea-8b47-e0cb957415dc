"use client"

import { SettingsModal } from "@/components/settings/SettingsModal"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import {
    BookOpen,
    HelpCircle,
    Logs,
    Plus,
    Settings,
    User
} from "lucide-react"
import { usePathname, useRouter } from "next/navigation"
import { useState } from "react"

export function OutermostSidebar() {
  const [isHovered, setIsHovered] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  const handleKnowledgeClick = () => {
    router.push('/dashboard/knowledge')
  }

  const handleSettingsClick = () => {
    setIsSettingsOpen(true)
  }

  const isActivePath = (path: string) => {
    return pathname.startsWith(path)
  }

  return (
    <div
      className={cn(
        "bg-background border-r border-border flex flex-col transition-all duration-200 ease-in-out fixed inset-y-0 left-0 z-40",
        isHovered ? "w-64 shadow-lg main-content-overlay" : "w-16"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center flex-shrink-0">
            <User className="w-4 h-4 text-white" />
          </div>
          {isHovered && (
            <div className="flex-1 transition-opacity duration-200 ml-1">
              <div className="text-foreground font-medium text-sm whitespace-nowrap overflow-hidden">
                Barun Debnath&apos;s workspace
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Workflows Section */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between mb-3">
          {isHovered && (
            <span className="text-muted-foreground text-sm font-medium">Workflows</span>
          )}
          {isHovered && (
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground">
              <Plus className="w-4 h-4" />
            </Button>
          )}
        </div>
        
        {/* Workflow Items */}
        <div className="space-y-2">
          <div className="flex items-center space-x-3 text-foreground cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md p-2 transition-colors">
            <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-medium">A</span>
            </div>
            {isHovered && (
              <div className="ml-1">
                <div className="text-sm whitespace-nowrap overflow-hidden">amber-aurora</div>
                <div className="text-xs text-muted-foreground mt-0.5">Saved about 16 hours ago</div>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-3 text-foreground cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md p-2 transition-colors">
            <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-medium">D</span>
            </div>
            {isHovered && (
              <span className="text-sm whitespace-nowrap overflow-hidden ml-1">default-agent</span>
            )}
          </div>
        </div>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 px-4 space-y-2">
        <div className="flex items-center space-x-3 text-muted-foreground hover:text-foreground cursor-pointer py-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md transition-colors">
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <Logs className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Logs</span>
          )}
        </div>

        <div
          onClick={handleKnowledgeClick}
          className={cn(
            "flex items-center space-x-3 cursor-pointer py-2 rounded-md transition-colors",
            isActivePath("/dashboard/knowledge")
              ? "text-foreground bg-neutral-100 dark:bg-neutral-800"
              : "text-muted-foreground hover:text-foreground hover:bg-neutral-100 dark:hover:bg-neutral-800"
          )}
        >
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <BookOpen className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Knowledge</span>
          )}
        </div>

        <div
          onClick={handleSettingsClick}
          className="flex items-center space-x-3 text-muted-foreground hover:text-foreground cursor-pointer py-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md transition-colors"
        >
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <Settings className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Settings</span>
          )}
        </div>
      </div>

      {/* Bottom Section */}
      <div className="p-4 border-t border-border">
        <div className="flex items-center space-x-3 text-muted-foreground hover:text-foreground cursor-pointer py-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md transition-colors">
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <User className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Invite members</span>
          )}
        </div>

        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
              <div className="w-4 h-4 border border-neutral-400 dark:border-neutral-600 rounded"></div>
            </div>
            {isHovered && (
              <span className="text-muted-foreground text-sm ml-1">Copy</span>
            )}
          </div>
          
          {isHovered && (
            <div className="flex space-x-2">
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground">
                <HelpCircle className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground">
                <div className="w-4 h-4 border border-neutral-400 dark:border-neutral-600 rounded"></div>
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Settings Modal */}
      <SettingsModal
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
      />
    </div>
  )
}
